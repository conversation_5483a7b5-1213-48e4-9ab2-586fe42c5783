'use client'

import { useState, useRef, useEffect } from 'react'
import { useTheme } from 'next-themes'
import {
  Bo<PERSON>,
  User,
  Send,
  Loader2,
  Sparkles,
  MessageCircle,
  TrendingUp,
  Package,
  CreditCard,
  BarChart3,
  Lightbulb,
  HelpCircle,
  Copy,
  Check,
  RotateCcw,
  Volume2,
  VolumeX,
  Zap,
  Brain
} from 'lucide-react'
import { motion } from 'framer-motion'

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  isTyping?: boolean
}

const quickPrompts = [
  {
    icon: TrendingUp,
    title: 'Sales Analysis',
    prompt: 'Analyze my current sales performance and suggest improvements'
  },
  {
    icon: Package,
    title: 'Inventory Management',
    prompt: 'Help me optimize my inventory levels and identify slow-moving products'
  },
  {
    icon: CreditCard,
    title: 'Debt Management',
    prompt: 'Provide strategies for managing customer debts effectively'
  },
  {
    icon: BarChart3,
    title: 'Business Insights',
    prompt: 'Give me insights on how to grow my sari-sari store business'
  },
  {
    icon: Lightbulb,
    title: 'Marketing Ideas',
    prompt: 'Suggest marketing strategies to attract more customers'
  },
  {
    icon: HelpCircle,
    title: 'General Help',
    prompt: 'What can you help me with regarding my store management?'
  }
]

export default function AISupport() {
  const { resolvedTheme } = useTheme()
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Welcome to AI Support! I\'m here to help you manage your Revantad Store more effectively. I can assist with business analytics, inventory management, customer relationships, financial planning, and much more. How can I help you today?',
      timestamp: new Date()
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)
  const [isSoundEnabled, setIsSoundEnabled] = useState(true)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const sendMessage = async (messageContent?: string) => {
    const content = messageContent || inputMessage.trim()
    if (!content || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    // Add typing indicator
    const typingMessage: Message = {
      id: 'typing',
      type: 'ai',
      content: '',
      timestamp: new Date(),
      isTyping: true
    }
    setMessages(prev => [...prev, typingMessage])

    try {
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content,
          context: 'ai-support'
        })
      })

      const data = await response.json()

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing'))

      if (data.success) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: data.response,
          timestamp: new Date()
        }
        setMessages(prev => [...prev, aiMessage])

        // Play notification sound
        playNotificationSound()
      } else {
        throw new Error(data.error || 'Failed to get AI response')
      }
    } catch (error) {
      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing'))

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: 'Sorry, I encountered an error. Please try again later.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    })
  }

  const copyMessage = async (content: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopiedMessageId(messageId)
      setTimeout(() => setCopiedMessageId(null), 2000)
    } catch (error) {
      console.error('Failed to copy message:', error)
    }
  }

  const clearChat = () => {
    setMessages([
      {
        id: '1',
        type: 'ai',
        content: 'Welcome to AI Support! I\'m here to help you manage your Revantad Store more effectively. I can assist with business analytics, inventory management, customer relationships, financial planning, and much more. How can I help you today?',
        timestamp: new Date()
      }
    ])
  }

  const playNotificationSound = () => {
    if (!isSoundEnabled) return
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1)

      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.2)
    } catch (error) {
      console.log('Audio not supported')
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header with Controls */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <motion.div
              className="p-3 rounded-xl mr-3"
              style={{
                background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                boxShadow: '0 4px 8px rgba(139, 92, 246, 0.3)'
              }}
              whileHover={{ scale: 1.05 }}
            >
              <Brain className="w-6 h-6 text-white" />
            </motion.div>
            <div>
              <h2
                className="text-xl font-bold flex items-center"
                style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
              >
                AI Business Assistant
              </h2>
              <p
                className="text-sm opacity-80"
                style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#64748b' }}
              >
                Intelligent support for your sari-sari store operations
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsSoundEnabled(!isSoundEnabled)}
              className="p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-105"
              title={isSoundEnabled ? 'Disable sound' : 'Enable sound'}
            >
              {isSoundEnabled ? (
                <Volume2 className="w-4 h-4" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
              ) : (
                <VolumeX className="w-4 h-4" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
              )}
            </button>
            <button
              onClick={clearChat}
              className="p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-105"
              title="Clear chat"
            >
              <RotateCcw className="w-4 h-4" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
            </button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-6">
        <h3
          className="text-lg font-semibold mb-4 flex items-center"
          style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
        >
          <Zap className="w-5 h-5 mr-2 text-purple-500" />
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {quickPrompts.map((prompt, index) => {
            const Icon = prompt.icon
            return (
              <motion.button
                key={index}
                onClick={() => sendMessage(prompt.prompt)}
                className="p-5 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 text-left relative overflow-hidden group"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)',
                  borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',
                  boxShadow: resolvedTheme === 'dark'
                    ? '0 4px 6px rgba(0, 0, 0, 0.1)'
                    : '0 4px 6px rgba(0, 0, 0, 0.05)'
                }}
                whileHover={{
                  scale: 1.03,
                  boxShadow: resolvedTheme === 'dark'
                    ? '0 8px 25px rgba(139, 92, 246, 0.2)'
                    : '0 8px 25px rgba(139, 92, 246, 0.15)'
                }}
                whileTap={{ scale: 0.98 }}
                disabled={isLoading}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                {/* Hover gradient overlay */}
                <div
                  className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 rounded-xl"
                  style={{
                    background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
                  }}
                />

                {/* Content */}
                <div className="relative z-10">
                  <div className="flex items-center mb-3">
                    <motion.div
                      className="p-2 rounded-lg mr-3"
                      style={{
                        backgroundColor: resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.2)' : 'rgba(139, 92, 246, 0.1)',
                        border: `1px solid ${resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.3)' : 'rgba(139, 92, 246, 0.2)'}`
                      }}
                      whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                      <Icon className="w-5 h-5 text-purple-500" />
                    </motion.div>
                    <span
                      className="font-semibold text-sm"
                      style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                    >
                      {prompt.title}
                    </span>
                  </div>
                  <p
                    className="text-xs leading-relaxed"
                    style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#64748b' }}
                  >
                    {prompt.prompt}
                  </p>

                  {/* Action indicator */}
                  <div className="flex items-center mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="flex-1 h-px bg-gradient-to-r from-purple-500/20 to-transparent" />
                    <span
                      className="text-xs font-medium ml-2 text-purple-500"
                    >
                      Click to ask
                    </span>
                  </div>
                </div>
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Chat Interface */}
      <div className="flex-1 flex flex-col">
        <motion.div
          className="flex-1 rounded-xl border p-6 mb-4 overflow-hidden backdrop-blur-sm"
          style={{
            background: resolvedTheme === 'dark'
              ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%)'
              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(249, 250, 251, 0.8) 100%)',
            borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',
            boxShadow: resolvedTheme === 'dark'
              ? '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
              : '0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8)'
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          {/* Messages */}
          <div className="h-96 overflow-y-auto space-y-4 mb-6 chat-scroll">
            {messages.map((message) => {
              if (message.isTyping) {
                return (
                  <motion.div
                    key={message.id}
                    className="flex justify-start"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    <div className="flex items-start space-x-3">
                      <div
                        className="p-2.5 rounded-full"
                        style={{ backgroundColor: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }}
                      >
                        <Bot className="w-5 h-5 text-white" />
                      </div>
                      <div
                        className="p-4 rounded-2xl rounded-tl-md flex items-center space-x-3"
                        style={{
                          backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.4)' : 'rgba(243, 244, 246, 0.9)'
                        }}
                      >
                        <div className="flex space-x-1">
                          <motion.div
                            className="w-2 h-2 rounded-full typing-dot"
                            style={{ backgroundColor: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }}
                          />
                          <motion.div
                            className="w-2 h-2 rounded-full typing-dot"
                            style={{ backgroundColor: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }}
                          />
                          <motion.div
                            className="w-2 h-2 rounded-full typing-dot"
                            style={{ backgroundColor: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }}
                          />
                        </div>
                        <span className="text-sm" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>
                          AI is analyzing your request...
                        </span>
                      </div>
                    </div>
                  </motion.div>
                )
              }

              return (
                <motion.div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.3, type: 'spring', stiffness: 200 }}
                >
                <div className={`flex items-start space-x-3 max-w-[85%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''} group`}>
                  <motion.div
                    className="p-2.5 rounded-full flex-shrink-0 relative overflow-hidden"
                    style={{
                      backgroundColor: message.type === 'user'
                        ? (resolvedTheme === 'dark' ? '#22c55e' : '#16a34a')
                        : (resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed')
                    }}
                    whileHover={{ scale: 1.1 }}
                  >
                    {/* Shimmer effect */}
                    <div
                      className="absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-300"
                      style={{
                        background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)',
                        transform: 'translateX(-100%)',
                        animation: 'shimmer 1.5s infinite'
                      }}
                    />
                    {message.type === 'user' ? (
                      <User className="w-5 h-5 text-white relative z-10" />
                    ) : (
                      <Bot className="w-5 h-5 text-white relative z-10" />
                    )}
                  </motion.div>
                  <div className="relative flex-1">
                    <motion.div
                      className={`p-4 rounded-2xl ${message.type === 'user' ? 'rounded-tr-md' : 'rounded-tl-md'} relative overflow-hidden`}
                      style={{
                        backgroundColor: message.type === 'user'
                          ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')
                          : (resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.4)' : 'rgba(243, 244, 246, 0.9)'),
                        color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',
                        border: `1px solid ${message.type === 'user'
                          ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.3)' : 'rgba(34, 197, 94, 0.2)')
                          : (resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.3)' : 'rgba(139, 92, 246, 0.2)')}`
                      }}
                      whileHover={{ scale: 1.01 }}
                    >
                      <p className="text-sm whitespace-pre-wrap leading-relaxed mb-3">{message.content}</p>
                      <div className="flex items-center justify-between">
                        <p
                          className="text-xs opacity-70"
                          style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                        >
                          {formatTime(message.timestamp)}
                        </p>
                        {message.type === 'ai' && (
                          <button
                            onClick={() => copyMessage(message.content, message.id)}
                            className="opacity-0 group-hover:opacity-100 p-1.5 rounded-lg transition-all duration-200 hover:bg-gray-200 dark:hover:bg-gray-600"
                            title="Copy message"
                          >
                            {copiedMessageId === message.id ? (
                              <Check className="w-4 h-4 text-green-500" />
                            ) : (
                              <Copy className="w-4 h-4" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                            )}
                          </button>
                        )}
                      </div>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            ))}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="border-t pt-4" style={{ borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.2)' : 'rgba(229, 231, 235, 0.8)' }}>
            <div className="flex items-end space-x-3">
              <div className="flex-1 relative">
                <textarea
                  ref={inputRef as any}
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me anything about your store management..."
                  disabled={isLoading}
                  rows={1}
                  className="w-full p-4 pr-16 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',
                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',
                    minHeight: '52px',
                    maxHeight: '120px'
                  }}
                  onInput={(e) => {
                    const target = e.target as HTMLTextAreaElement
                    target.style.height = 'auto'
                    target.style.height = Math.min(target.scrollHeight, 120) + 'px'
                  }}
                />

                {/* Character count */}
                <div
                  className="absolute bottom-2 right-2 text-xs opacity-50"
                  style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                >
                  {inputMessage.length}/1000
                </div>
              </div>

              <motion.button
                onClick={() => sendMessage()}
                disabled={!inputMessage.trim() || isLoading}
                className="p-4 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500 relative overflow-hidden"
                style={{
                  background: !inputMessage.trim() || isLoading
                    ? (resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.5)' : 'rgba(156, 163, 175, 0.5)')
                    : 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                  boxShadow: !inputMessage.trim() || isLoading
                    ? 'none'
                    : '0 4px 12px rgba(139, 92, 246, 0.4)'
                }}
                whileHover={!inputMessage.trim() || isLoading ? {} : { scale: 1.05 }}
                whileTap={!inputMessage.trim() || isLoading ? {} : { scale: 0.95 }}
              >
                {/* Animated background */}
                {inputMessage.trim() && !isLoading && (
                  <div className="absolute inset-0 rounded-xl">
                    <div
                      className="absolute inset-0 rounded-xl animate-pulse opacity-20"
                      style={{ background: 'linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%)' }}
                    />
                  </div>
                )}

                <div className="relative z-10">
                  {isLoading ? (
                    <Loader2 className="w-5 h-5 text-white animate-spin" />
                  ) : (
                    <Send className="w-5 h-5 text-white" />
                  )}
                </div>
              </motion.button>
            </div>

            {/* Quick suggestions */}
            {!inputMessage && (
              <motion.div
                className="mt-3 flex flex-wrap gap-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                {['How to increase sales?', 'Inventory best practices', 'Customer retention tips', 'Financial planning'].map((suggestion, index) => (
                  <motion.button
                    key={suggestion}
                    onClick={() => setInputMessage(suggestion)}
                    className="px-3 py-1.5 text-xs rounded-full border transition-all duration-200 hover:scale-105"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.1)' : 'rgba(139, 92, 246, 0.05)',
                      borderColor: resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.3)' : 'rgba(139, 92, 246, 0.2)',
                      color: resolvedTheme === 'dark' ? '#a855f7' : '#7c3aed'
                    }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                  >
                    {suggestion}
                  </motion.button>
                ))}
              </motion.div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  )
}
