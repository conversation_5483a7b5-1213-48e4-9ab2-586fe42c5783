[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts": "34"}, {"size": 5246, "mtime": 1752212817079, "results": "35", "hashOfConfig": "36"}, {"size": 1837, "mtime": 1751939415459, "results": "37", "hashOfConfig": "36"}, {"size": 2674, "mtime": 1752216983535, "results": "38", "hashOfConfig": "36"}, {"size": 2623, "mtime": 1752213773252, "results": "39", "hashOfConfig": "36"}, {"size": 2412, "mtime": 1752216948323, "results": "40", "hashOfConfig": "36"}, {"size": 1415, "mtime": 1751939500686, "results": "41", "hashOfConfig": "36"}, {"size": 11603, "mtime": 1752190925916, "results": "42", "hashOfConfig": "36"}, {"size": 1367, "mtime": 1752150087374, "results": "43", "hashOfConfig": "36"}, {"size": 8374, "mtime": 1752190941247, "results": "44", "hashOfConfig": "36"}, {"size": 753, "mtime": 1751948571543, "results": "45", "hashOfConfig": "36"}, {"size": 10478, "mtime": 1752061529987, "results": "46", "hashOfConfig": "36"}, {"size": 7962, "mtime": 1752213842534, "results": "47", "hashOfConfig": "36"}, {"size": 15574, "mtime": 1751949060717, "results": "48", "hashOfConfig": "36"}, {"size": 9709, "mtime": 1752216882714, "results": "49", "hashOfConfig": "36"}, {"size": 7439, "mtime": 1752217005596, "results": "50", "hashOfConfig": "36"}, {"size": 7823, "mtime": 1752061478940, "results": "51", "hashOfConfig": "36"}, {"size": 12114, "mtime": 1752217040928, "results": "52", "hashOfConfig": "36"}, {"size": 11393, "mtime": 1752213786142, "results": "53", "hashOfConfig": "36"}, {"size": 1202, "mtime": 1752212336426, "results": "54", "hashOfConfig": "36"}, {"size": 1188, "mtime": 1751950590317, "results": "55", "hashOfConfig": "36"}, {"size": 8850, "mtime": 1751938233472, "results": "56", "hashOfConfig": "36"}, {"size": 9147, "mtime": 1752061416183, "results": "57", "hashOfConfig": "36"}, {"size": 1905, "mtime": 1751950118275, "results": "58", "hashOfConfig": "36"}, {"size": 13317, "mtime": 1751949288409, "results": "59", "hashOfConfig": "36"}, {"size": 17247, "mtime": 1752203286787, "results": "60", "hashOfConfig": "36"}, {"size": 344, "mtime": 1752212829480, "results": "61", "hashOfConfig": "36"}, {"size": 3296, "mtime": 1752212374913, "results": "62", "hashOfConfig": "36"}, {"size": 2521, "mtime": 1751950064561, "results": "63", "hashOfConfig": "36"}, {"size": 5120, "mtime": 1752212844330, "results": "64", "hashOfConfig": "36"}, {"size": 1025, "mtime": 1752217088143, "results": "65", "hashOfConfig": "36"}, {"size": 4966, "mtime": 1752212675188, "results": "66", "hashOfConfig": "36"}, {"size": 1266, "mtime": 1752212686019, "results": "67", "hashOfConfig": "36"}, {"size": 2234, "mtime": 1752217075185, "results": "68", "hashOfConfig": "36"}, {"size": 5694, "mtime": 1752212408642, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, "10tv68i", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 3, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 7, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\admin\\page.tsx", ["172", "173"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\route.ts", ["174"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\debts\\[id]\\route.ts", ["175"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\route.ts", ["176", "177"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts", ["178"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\upload\\route.ts", ["179"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\landing\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\layout.tsx", ["180"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\login\\page.tsx", ["181", "182", "183"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\page.tsx", ["184"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\AdminHeader.tsx", ["185", "186", "187"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\APIGraphing.tsx", ["188", "189", "190"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Calendar.tsx", ["191"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DashboardStats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtModal.tsx", ["192", "193"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\DebtsSection.tsx", ["194", "195", "196", "197", "198", "199", "200"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\FamilyGallery.tsx", ["201"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\History.tsx", ["202", "203"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductModal.tsx", ["204", "205", "206"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProductsSection.tsx", ["207", "208", "209", "210", "211"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ProtectedRoute.tsx", ["212", "213"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Settings.tsx", ["214", "215", "216", "217"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\Sidebar.tsx", ["218"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\api-utils.ts", ["219", "220", "221", "222", "223", "224"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\cloudinary.ts", ["225"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\env.ts", ["226", "227"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\lib\\supabase.ts", ["228"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\types\\index.ts", ["229", "230", "231", "232", "233"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\utils\\index.ts", ["234", "235", "236", "237", "238"], [], {"ruleId": "239", "severity": 1, "message": "240", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 39, "fix": "242"}, {"ruleId": "239", "severity": 1, "message": "243", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 39, "fix": "244"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 1, "column": 1, "nodeType": "241", "endLine": 1, "endColumn": 56, "fix": "245"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 1, "column": 1, "nodeType": "241", "endLine": 1, "endColumn": 56, "fix": "246"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 1, "column": 1, "nodeType": "241", "endLine": 1, "endColumn": 42, "fix": "247"}, {"ruleId": "239", "severity": 1, "message": "248", "line": 3, "column": 1, "nodeType": "241", "endLine": 12, "endColumn": 25, "fix": "249"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 1, "column": 1, "nodeType": "241", "endLine": 1, "endColumn": 56, "fix": "250"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 1, "column": 1, "nodeType": "241", "endLine": 1, "endColumn": 56, "fix": "251"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 2, "column": 1, "nodeType": "241", "endLine": 2, "endColumn": 51, "fix": "252"}, {"ruleId": "239", "severity": 1, "message": "253", "line": 3, "column": 1, "nodeType": "241", "endLine": 3, "endColumn": 44, "fix": "254"}, {"ruleId": "239", "severity": 1, "message": "255", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 44, "fix": "256"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 7, "column": 1, "nodeType": "241", "endLine": 7, "endColumn": 29, "fix": "257"}, {"ruleId": "239", "severity": 1, "message": "258", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 44, "fix": "259"}, {"ruleId": "239", "severity": 1, "message": "260", "line": 3, "column": 1, "nodeType": "241", "endLine": 3, "endColumn": 44, "fix": "261"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 6, "column": 1, "nodeType": "241", "endLine": 6, "endColumn": 39, "fix": "262"}, {"ruleId": "263", "severity": 1, "message": "264", "line": 58, "column": 5, "nodeType": "265", "messageId": "266", "endLine": 58, "endColumn": 16, "suggestions": "267"}, {"ruleId": "239", "severity": 1, "message": "268", "line": 3, "column": 1, "nodeType": "241", "endLine": 3, "endColumn": 44, "fix": "269"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 50, "column": 27, "nodeType": "272", "messageId": "273", "endLine": 50, "endColumn": 30, "suggestions": "274"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 110, "column": 27, "nodeType": "272", "messageId": "273", "endLine": 110, "endColumn": 30, "suggestions": "275"}, {"ruleId": "239", "severity": 1, "message": "276", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 111, "fix": "277"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 33, "fix": "278"}, {"ruleId": "239", "severity": 1, "message": "276", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 33, "fix": "279"}, {"ruleId": "239", "severity": 1, "message": "276", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 75, "fix": "280"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 5, "column": 1, "nodeType": "241", "endLine": 5, "endColumn": 39, "fix": "281"}, {"ruleId": "239", "severity": 1, "message": "243", "line": 5, "column": 1, "nodeType": "241", "endLine": 5, "endColumn": 39, "fix": "282"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 6, "column": 1, "nodeType": "241", "endLine": 6, "endColumn": 36, "fix": "283"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 7, "column": 1, "nodeType": "241", "endLine": 7, "endColumn": 46, "fix": "284"}, {"ruleId": "239", "severity": 1, "message": "285", "line": 7, "column": 1, "nodeType": "241", "endLine": 7, "endColumn": 46, "fix": "286"}, {"ruleId": "239", "severity": 1, "message": "287", "line": 8, "column": 1, "nodeType": "241", "endLine": 8, "endColumn": 34, "fix": "288"}, {"ruleId": "239", "severity": 1, "message": "276", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 97, "fix": "289"}, {"ruleId": "239", "severity": 1, "message": "276", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 82, "fix": "290"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 13, "column": 13, "nodeType": "272", "messageId": "273", "endLine": 13, "endColumn": 16, "suggestions": "291"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 50, "fix": "292"}, {"ruleId": "239", "severity": 1, "message": "276", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 50, "fix": "293"}, {"ruleId": "294", "severity": 1, "message": "295", "line": 151, "column": 19, "nodeType": "296", "endLine": 155, "endColumn": 21}, {"ruleId": "239", "severity": 1, "message": "260", "line": 3, "column": 1, "nodeType": "241", "endLine": 3, "endColumn": 44, "fix": "297"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 5, "column": 1, "nodeType": "241", "endLine": 5, "endColumn": 39, "fix": "298"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 6, "column": 1, "nodeType": "241", "endLine": 6, "endColumn": 42, "fix": "299"}, {"ruleId": "239", "severity": 1, "message": "300", "line": 6, "column": 1, "nodeType": "241", "endLine": 6, "endColumn": 42, "fix": "301"}, {"ruleId": "294", "severity": 1, "message": "295", "line": 149, "column": 17, "nodeType": "296", "endLine": 153, "endColumn": 19}, {"ruleId": "239", "severity": 1, "message": "240", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 44, "fix": "302"}, {"ruleId": "239", "severity": 1, "message": "258", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 44, "fix": "303"}, {"ruleId": "239", "severity": 1, "message": "276", "line": 4, "column": 1, "nodeType": "241", "endLine": 4, "endColumn": 100, "fix": "304"}, {"ruleId": "263", "severity": 1, "message": "264", "line": 68, "column": 5, "nodeType": "265", "messageId": "266", "endLine": 68, "endColumn": 16, "suggestions": "305"}, {"ruleId": "263", "severity": 1, "message": "264", "line": 74, "column": 5, "nodeType": "265", "messageId": "266", "endLine": 74, "endColumn": 16, "suggestions": "306"}, {"ruleId": "263", "severity": 1, "message": "264", "line": 80, "column": 5, "nodeType": "265", "messageId": "266", "endLine": 80, "endColumn": 16, "suggestions": "307"}, {"ruleId": "239", "severity": 1, "message": "260", "line": 3, "column": 1, "nodeType": "241", "endLine": 3, "endColumn": 44, "fix": "308"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 3, "column": 1, "nodeType": "241", "endLine": 3, "endColumn": 43, "fix": "309"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 99, "column": 44, "nodeType": "272", "messageId": "273", "endLine": 99, "endColumn": 47, "suggestions": "310"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 124, "column": 21, "nodeType": "272", "messageId": "273", "endLine": 124, "endColumn": 24, "suggestions": "311"}, {"ruleId": "312", "severity": 1, "message": "313", "line": 129, "column": 12, "nodeType": null, "messageId": "314", "endLine": 129, "endColumn": 17}, {"ruleId": "270", "severity": 1, "message": "271", "line": 134, "column": 65, "nodeType": "272", "messageId": "273", "endLine": 134, "endColumn": 68, "suggestions": "315"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 151, "column": 44, "nodeType": "272", "messageId": "273", "endLine": 151, "endColumn": 47, "suggestions": "316"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 1, "column": 1, "nodeType": "241", "endLine": 1, "endColumn": 46, "fix": "317"}, {"ruleId": "263", "severity": 1, "message": "264", "line": 146, "column": 7, "nodeType": "265", "messageId": "266", "endLine": 146, "endColumn": 18, "suggestions": "318"}, {"ruleId": "263", "severity": 1, "message": "264", "line": 147, "column": 7, "nodeType": "265", "messageId": "266", "endLine": 147, "endColumn": 18, "suggestions": "319"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 1, "column": 1, "nodeType": "241", "endLine": 1, "endColumn": 53, "fix": "320"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 29, "column": 32, "nodeType": "272", "messageId": "273", "endLine": 29, "endColumn": 35, "suggestions": "321"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 49, "column": 29, "nodeType": "272", "messageId": "273", "endLine": 49, "endColumn": 32, "suggestions": "322"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 58, "column": 34, "nodeType": "272", "messageId": "273", "endLine": 58, "endColumn": 37, "suggestions": "323"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 96, "column": 30, "nodeType": "272", "messageId": "273", "endLine": 96, "endColumn": 33, "suggestions": "324"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 97, "column": 35, "nodeType": "272", "messageId": "273", "endLine": 97, "endColumn": 38, "suggestions": "325"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 3, "column": 1, "nodeType": "241", "endLine": 3, "endColumn": 65, "fix": "326"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 82, "column": 47, "nodeType": "272", "messageId": "273", "endLine": 82, "endColumn": 50, "suggestions": "327"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 91, "column": 47, "nodeType": "272", "messageId": "273", "endLine": 91, "endColumn": 50, "suggestions": "328"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 191, "column": 46, "nodeType": "272", "messageId": "273", "endLine": 191, "endColumn": 49, "suggestions": "329"}, {"ruleId": "270", "severity": 1, "message": "271", "line": 191, "column": 56, "nodeType": "272", "messageId": "273", "endLine": 191, "endColumn": 59, "suggestions": "330"}, "import/order", "There should be at least one empty line between import groups", "ImportDeclaration", {"range": "331", "text": "332"}, "`next-themes` import should occur before import of `react`", {"range": "333", "text": "334"}, {"range": "335", "text": "332"}, {"range": "336", "text": "332"}, {"range": "337", "text": "332"}, "`@/lib/api-utils` import should occur before import of `@/lib/supabase`", {"range": "338", "text": "339"}, {"range": "340", "text": "332"}, {"range": "341", "text": "332"}, {"range": "342", "text": "332"}, "`react` import should occur after import of `next/link`", {"range": "343", "text": "344"}, "`next/navigation` import should occur after import of `next/link`", {"range": "345", "text": "346"}, {"range": "347", "text": "332"}, "`next/navigation` import should occur before import of `react`", {"range": "348", "text": "349"}, "`react` import should occur after import of `next-themes`", {"range": "350", "text": "351"}, {"range": "352", "text": "332"}, "no-console", "Unexpected console statement. Only these console methods are allowed: warn, error.", "MemberExpression", "limited", ["353"], "`react` import should occur after import of `lucide-react`", {"range": "354", "text": "355"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["356", "357"], ["358", "359"], "`lucide-react` import should occur before import of `react`", {"range": "360", "text": "361"}, {"range": "362", "text": "332"}, {"range": "363", "text": "364"}, {"range": "365", "text": "366"}, {"range": "367", "text": "332"}, {"range": "368", "text": "369"}, {"range": "370", "text": "332"}, {"range": "371", "text": "332"}, "`@/lib/supabase` import should occur before import of `./DebtModal`", {"range": "372", "text": "373"}, "`date-fns` import should occur before import of `react`", {"range": "374", "text": "375"}, {"range": "376", "text": "377"}, {"range": "378", "text": "379"}, ["380", "381"], {"range": "382", "text": "332"}, {"range": "383", "text": "384"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", {"range": "385", "text": "386"}, {"range": "387", "text": "332"}, {"range": "388", "text": "332"}, "`./ProductModal` import should occur after import of `@/lib/supabase`", {"range": "389", "text": "390"}, {"range": "391", "text": "332"}, {"range": "392", "text": "349"}, {"range": "393", "text": "394"}, ["395"], ["396"], ["397"], {"range": "398", "text": "399"}, {"range": "400", "text": "332"}, ["401", "402"], ["403", "404"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", ["405", "406"], ["407", "408"], {"range": "409", "text": "332"}, ["410"], ["411"], {"range": "412", "text": "332"}, ["413", "414"], ["415", "416"], ["417", "418"], ["419", "420"], ["421", "422"], {"range": "423", "text": "332"}, ["424", "425"], ["426", "427"], ["428", "429"], ["430", "431"], [96, 96], "\n", [14, 97], "import { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [55, 55], [55, 55], [41, 41], [42, 300], "import {\n  successResponse,\n  errorResponse,\n  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  validateRequestBody,\n  validateRequiredFields,\n  handleDatabaseError,\n  parsePaginationParams,\n  handleCorsPreflightRequest\n} from '@/lib/api-utils'\nimport { supabase } from '@/lib/supabase'\n", [55, 55], [55, 55], [88, 88], [14, 244], "import { useRouter } from 'next/navigation'\nimport { motion } from 'framer-motion'\nimport { Eye, EyeOff, Lock, Mail, ArrowRight, Store } from 'lucide-react'\nimport Link from 'next/link'\nimport { useState, useEffect } from 'react'\n", [58, 244], "import { motion } from 'framer-motion'\nimport { Eye, EyeOff, Lock, Mail, ArrowRight, Store } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\n", [243, 243], [14, 92], "import { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n", [14, 218], "import { Search, Home, Package, Users, Image, Moon, Sun, LogOut, User } from 'lucide-react'\nimport Link from 'next/link'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [217, 217], {"fix": "432", "messageId": "433", "data": "434", "desc": "435"}, [14, 183], "import ReactECharts from 'echarts-for-react'\nimport { TrendingUp, DollarSign, Package, Users, Calendar } from 'lucide-react'\nimport { useEffect, useState } from 'react'\n", {"messageId": "436", "fix": "437", "desc": "438"}, {"messageId": "439", "fix": "440", "desc": "441"}, {"messageId": "436", "fix": "442", "desc": "438"}, {"messageId": "439", "fix": "443", "desc": "441"}, [14, 158], "import { ChevronLeft, ChevronRight, Plus, Clock, MapPin, Users, Calendar as CalendarIcon } from 'lucide-react'\nimport { useState } from 'react'\n", [90, 90], [14, 91], "import { X } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n", [14, 133], "import { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n", [171, 171], [14, 172], "import { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\n", [207, 207], [253, 253], [172, 254], "import { CustomerDebt } from '@/lib/supabase'\nimport DebtModal from './DebtModal'\n", [14, 288], "import { format } from 'date-fns'\nimport { useState, useEffect } from 'react'\nimport { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport DebtModal from './DebtModal'\nimport { CustomerDebt } from '@/lib/supabase'\n", [14, 144], "import { Upload, Heart, Share2, Download, Trash2, Plus, Image as ImageIcon } from 'lucide-react'\nimport { useState } from 'react'\n", [14, 129], "import { Search, Download, Clock, User, Package, DollarSign } from 'lucide-react'\nimport { useState } from 'react'\n", {"messageId": "436", "fix": "444", "desc": "438"}, {"messageId": "439", "fix": "445", "desc": "441"}, [107, 107], [14, 108], "import { X, Upload, Package } from 'lucide-react'\nimport { useState, useEffect } from 'react'\n", [14, 164], "import { Plus, Edit, Trash2, Search, Package } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [163, 163], [205, 205], [164, 267], "import { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'\nimport ProductModal from './ProductModal'\n", [91, 91], [14, 92], [14, 147], "import { Save, User, Store, Bell, Shield, Palette, Database, Download, Upload } from 'lucide-react'\nimport { useState } from 'react'\n", {"fix": "446", "messageId": "433", "data": "447", "desc": "435"}, {"fix": "448", "messageId": "433", "data": "449", "desc": "435"}, {"fix": "450", "messageId": "433", "data": "451", "desc": "435"}, [14, 194], "import { BarChart3, History, Calendar, Settings, ChevronLeft, ChevronRight } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n", [114, 114], {"messageId": "436", "fix": "452", "desc": "438"}, {"messageId": "439", "fix": "453", "desc": "441"}, {"messageId": "436", "fix": "454", "desc": "438"}, {"messageId": "439", "fix": "455", "desc": "441"}, {"messageId": "436", "fix": "456", "desc": "438"}, {"messageId": "439", "fix": "457", "desc": "441"}, {"messageId": "436", "fix": "458", "desc": "438"}, {"messageId": "439", "fix": "459", "desc": "441"}, [45, 45], {"fix": "460", "messageId": "433", "data": "461", "desc": "435"}, {"fix": "462", "messageId": "433", "data": "463", "desc": "435"}, [52, 52], {"messageId": "436", "fix": "464", "desc": "438"}, {"messageId": "439", "fix": "465", "desc": "441"}, {"messageId": "436", "fix": "466", "desc": "438"}, {"messageId": "439", "fix": "467", "desc": "441"}, {"messageId": "436", "fix": "468", "desc": "438"}, {"messageId": "439", "fix": "469", "desc": "441"}, {"messageId": "436", "fix": "470", "desc": "438"}, {"messageId": "439", "fix": "471", "desc": "441"}, {"messageId": "436", "fix": "472", "desc": "438"}, {"messageId": "439", "fix": "473", "desc": "441"}, [93, 93], {"messageId": "436", "fix": "474", "desc": "438"}, {"messageId": "439", "fix": "475", "desc": "441"}, {"messageId": "436", "fix": "476", "desc": "438"}, {"messageId": "439", "fix": "477", "desc": "441"}, {"messageId": "436", "fix": "478", "desc": "438"}, {"messageId": "439", "fix": "479", "desc": "441"}, {"messageId": "436", "fix": "480", "desc": "438"}, {"messageId": "439", "fix": "481", "desc": "441"}, {"range": "482", "text": "483"}, "removeConsole", {"propertyName": "484"}, "Remove the console.log().", "suggestUnknown", {"range": "485", "text": "486"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "487", "text": "488"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "489", "text": "486"}, {"range": "490", "text": "488"}, {"range": "491", "text": "486"}, {"range": "492", "text": "488"}, {"range": "493", "text": "483"}, {"propertyName": "484"}, {"range": "494", "text": "483"}, {"propertyName": "484"}, {"range": "495", "text": "483"}, {"propertyName": "484"}, {"range": "496", "text": "486"}, {"range": "497", "text": "488"}, {"range": "498", "text": "486"}, {"range": "499", "text": "488"}, {"range": "500", "text": "486"}, {"range": "501", "text": "488"}, {"range": "502", "text": "486"}, {"range": "503", "text": "488"}, {"range": "504", "text": "483"}, {"propertyName": "484"}, {"range": "505", "text": "483"}, {"propertyName": "484"}, {"range": "506", "text": "486"}, {"range": "507", "text": "488"}, {"range": "508", "text": "486"}, {"range": "509", "text": "488"}, {"range": "510", "text": "486"}, {"range": "511", "text": "488"}, {"range": "512", "text": "486"}, {"range": "513", "text": "488"}, {"range": "514", "text": "486"}, {"range": "515", "text": "488"}, {"range": "516", "text": "486"}, {"range": "517", "text": "488"}, {"range": "518", "text": "486"}, {"range": "519", "text": "488"}, {"range": "520", "text": "486"}, {"range": "521", "text": "488"}, {"range": "522", "text": "486"}, {"range": "523", "text": "488"}, [1430, 1472], "", "log", [1229, 1232], "unknown", [1229, 1232], "never", [2526, 2529], [2526, 2529], [313, 316], [313, 316], [1835, 1875], [1992, 2024], [2171, 2203], [2131, 2134], [2131, 2134], [2784, 2787], [2784, 2787], [3046, 3049], [3046, 3049], [3475, 3478], [3475, 3478], [4248, 4309], [4316, 4522], [633, 636], [633, 636], [1044, 1047], [1044, 1047], [1211, 1214], [1211, 1214], [1967, 1970], [1967, 1970], [2027, 2030], [2027, 2030], [2410, 2413], [2410, 2413], [2612, 2615], [2612, 2615], [5337, 5340], [5337, 5340], [5347, 5350], [5347, 5350]]